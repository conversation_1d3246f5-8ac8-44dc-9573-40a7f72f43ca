[package]
name = "pdf_cropper"
version = "0.1.0"
edition = "2021"

[dependencies]
pdf = "0.8"
image = "0.24"
printpdf = "0.5"
anyhow = "1.0"
clap = { version = "4.0", features = ["derive"] }
poppler = { version = "0.6", features = ["render"] }  # For PDF rendering
cairo-rs = "0.20.12"  # Required by poppler

[lib]
name = "pdf_cropper"
path = "src/lib.rs"

[[bin]]
name = "pdf_cropper"
path = "src/main.rs"
