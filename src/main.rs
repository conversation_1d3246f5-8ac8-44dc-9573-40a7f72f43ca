// src/main.rs
use clap::{Arg, Command};
use std::path::Path;
use pdf_cropper::{crop_pdf, <PERSON>rop<PERSON>argins, CropperConfig};

fn main() {
    let matches = Command::new("PDF Auto-Cropper")
        .version("0.1.0")
        .about("Auto-crop PDF pages for better e-ink device viewing")
        .arg(
            Arg::new("input")
                .help("Input PDF file path")
                .required(true)
                .index(1)
        )
        .arg(
            Arg::new("output")
                .short('o')
                .long("output")
                .help("Output PDF path")
                .value_name("FILE")
        )
        .arg(
            Arg::new("left")
                .short('l')
                .long("left")
                .help("Left margin to crop (0.0-1.0)")
                .value_name("FLOAT")
        )
        .arg(
            Arg::new("right")
                .short('r')
                .long("right")
                .help("Right margin to crop (0.0-1.0)")
                .value_name("FLOAT")
        )
        .arg(
            Arg::new("top")
                .short('t')
                .long("top")
                .help("Top margin to crop (0.0-1.0)")
                .value_name("FLOAT")
        )
        .arg(
            Arg::new("bottom")
                .short('b')
                .long("bottom")
                .help("Bottom margin to crop (0.0-1.0)")
                .value_name("FLOAT")
        )
        .arg(
            Arg::new("buffer")
                .long("buffer")
                .help("Buffer space around auto-detected content (0.0-0.1)")
                .value_name("FLOAT")
                .default_value("0.01")
        )
        .arg(
            Arg::new("footer-height")
                .long("footer-height")
                .help("Height ratio to check for footer content (0.05-0.2)")
                .value_name("FLOAT")
                .default_value("0.1")
        )
        .get_matches();
    
    let input_path = matches.get_one::<String>("input").unwrap();
    let output_path = matches.get_one::<String>("output")
        .map(|s| s.clone())
        .unwrap_or_else(|| {
            let input = Path::new(input_path);
            let stem = input.file_stem().unwrap().to_str().unwrap();
            let extension = input.extension().unwrap().to_str().unwrap();
            format!("{}-cropped.{}", stem, extension)
        });
    
    // Validate input file
    if !Path::new(input_path).exists() {
        eprintln!("Error: Input file '{}' not found.", input_path);
        std::process::exit(1);
    }
    
    if !input_path.to_lowercase().ends_with(".pdf") {
        eprintln!("Error: Input file must be a PDF.");
        std::process::exit(1);
    }
    
    // Parse crop margins if provided
    let margins = if let (Some(l), Some(t), Some(r), Some(b)) = (
        matches.get_one::<String>("left"),
        matches.get_one::<String>("top"),
        matches.get_one::<String>("right"),
        matches.get_one::<String>("bottom")
    ) {
        // All crop values provided
        let left = l.parse::<f64>().unwrap_or_else(|_| {
            eprintln!("Error: Invalid left margin value");
            std::process::exit(1);
        });
        let top = t.parse::<f64>().unwrap_or_else(|_| {
            eprintln!("Error: Invalid top margin value");
            std::process::exit(1);
        });
        let right = r.parse::<f64>().unwrap_or_else(|_| {
            eprintln!("Error: Invalid right margin value");
            std::process::exit(1);
        });
        let bottom = b.parse::<f64>().unwrap_or_else(|_| {
            eprintln!("Error: Invalid bottom margin value");
            std::process::exit(1);
        });
        
        // Validate crop parameters
        for (name, value) in [("left", left), ("top", top), ("right", right), ("bottom", bottom)] {
            if value < 0.0 || value >= 1.0 {
                eprintln!("Error: {} margin must be between 0.0 and 1.0", name);
                std::process::exit(1);
            }
        }
        
        Some(CropMargins { left, top, right, bottom })
    } else if matches.get_one::<String>("left").is_some() || 
              matches.get_one::<String>("top").is_some() ||
              matches.get_one::<String>("right").is_some() ||
              matches.get_one::<String>("bottom").is_some() {
        eprintln!("Error: If specifying crop margins, all four values (left, top, right, bottom) must be provided");
        std::process::exit(1);
    } else {
        None
    };
    
    // Parse buffer parameter
    let buffer = matches.get_one::<String>("buffer").unwrap()
        .parse::<f64>().unwrap_or_else(|_| {
            eprintln!("Error: Invalid buffer value");
            std::process::exit(1);
        });
    
    if buffer < 0.0 || buffer > 0.1 {
        eprintln!("Error: Buffer value must be between 0.0 and 0.1");
        std::process::exit(1);
    }
    
    // Parse footer height parameter
    let footer_height = matches.get_one::<String>("footer-height").unwrap()
        .parse::<f64>().unwrap_or_else(|_| {
            eprintln!("Error: Invalid footer height value");
            std::process::exit(1);
        });
    
    if footer_height < 0.05 || footer_height > 0.2 {
        eprintln!("Error: Footer height must be between 0.05 and 0.2");
        std::process::exit(1);
    }
    
    // Create configuration
    let config = CropperConfig {
        buffer,
        footer_height_ratio: footer_height,
        ..CropperConfig::default()
    };
    
    println!("Processing: {}", input_path);
    println!("Output will be saved as: {}", output_path);
    
    match crop_pdf(input_path, &output_path, margins, &config) {
        Ok(()) => println!("Done! Your PDF has been cropped and optimized for e-ink viewing."),
        Err(e) => {
            eprintln!("Error processing PDF: {}", e);
            std::process::exit(1);
        }
    }
}